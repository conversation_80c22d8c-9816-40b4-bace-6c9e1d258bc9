import React, {useState, useEffect, useMemo, useCallback, useRef} from "react"; // Thêm useRef
import {Modal, Spin, Alert, Tabs, Button, Space} from "antd";
import {FileWordOutlined, ReloadOutlined, DownloadOutlined} from "@ant-design/icons";
import {isEqual} from "lodash";
import {memo} from "react";
import "./index.default.scss";
import {IWordViewerProps} from "./types";
// Import docx-preview (thay thế mammoth)
import {renderAsync, clear} from "docx-preview";

const WordViewerComponent: React.FC<IWordViewerProps> = ({
  fileUrl,
  fileName = "Word Document",
  open,
  onClose,
  width = "95vw",
  height = "85vh",
  destroyOnClose = true,
  containerStyle = {},
  viewerMode = "both",
}) => {
  const [loading, setLoading] = useState(false);
  const [arrayBuffer, setArrayBuffer] = useState<ArrayBuffer | null>(null); // Thay htmlContent bằng arrayBuffer
  const [error, setError] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("office");

  // Microsoft Office Online Viewer URL (giữ nguyên)
  const officeViewerUrl = useMemo(() => {
    if (!fileUrl) return "";
    return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
  }, [fileUrl]);

  // Load Word document using docx-preview
  const loadWordDocument = useCallback(async () => {
    if (!fileUrl || viewerMode === "office") return;

    setLoading(true);
    setError("");
    setArrayBuffer(null); // Reset buffer

    try {
      // Fetch the Word document
      const response = await fetch(fileUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const buffer = await response.arrayBuffer();
      setArrayBuffer(buffer); // Lưu buffer để render sau
    } catch (err) {
      console.error("Error loading Word document:", err);
      setError(err instanceof Error ? err.message : "Không thể tải file Word");
    } finally {
      setLoading(false);
    }
  }, [fileUrl, viewerMode]);

  // Load document when modal opens (giữ nguyên)
  useEffect(() => {
    if (open && fileUrl) {
      loadWordDocument();
    }
  }, [open, fileUrl, loadWordDocument]);

  // Reset state when modal closes (cập nhật để clear buffer)
  useEffect(() => {
    if (!open) {
      setArrayBuffer(null);
      setError("");
      setLoading(false);
    }
  }, [open]);

  // Default container style (giữ nguyên)
  const defaultContainerStyle: React.CSSProperties = {
    height: "100%",
    width: "100%",
    border: "1px solid #d9d9d9",
    borderRadius: "6px",
    overflow: "hidden",
    ...containerStyle,
  };

  // Office Viewer Component (giữ nguyên)
  const OfficeViewer = () => (
    <div style={defaultContainerStyle} className="office-viewer">
      <iframe
        src={officeViewerUrl}
        style={{
          width: "100%",
          height: "100%",
          border: "none",
        }}
        title={`Office Viewer - ${fileName}`}
      />
    </div>
  );

  // Docx Preview Viewer Component (thay thế MammothViewer)
  const DocxViewer = () => {
    const containerRef = useRef<HTMLDivElement>(null); // Ref cho container render

    // Render docx khi buffer ready và container mounted
    useEffect(() => {
      const container = containerRef.current;
      if (!container || !arrayBuffer) return;

      const renderDocx = async () => {
        try {
          // Options tùy chỉnh (có thể chỉnh thêm: ignoreWidth, ignoreHeight, etc.)
          await renderAsync(container, arrayBuffee, null, {
            ignoreWidth: false,
            ignoreHeight: false,
            ignoreFonts: false,
            breakPages: true,
            className: "docx-wrapper", // Class CSS tùy chỉnh nếu cần
            inWrapper: true,
          });
        } catch (err) {
          console.error("Error rendering DOCX:", err);
          setError(err instanceof Error ? err.message : "Không thể render file Word");
        }
      };

      renderDocx();

      // Cleanup khi unmount hoặc buffer thay đổi
      return () => {
        if (container) {
          clear(container);
        }
      };
    }, [arrayBuffer]);

    if (loading) {
      return (
        <div style={{...defaultContainerStyle, display: "flex", alignItems: "center", justifyContent: "center"}}>
          <Spin size="large" tip="Đang tải file Word..." />
        </div>
      );
    }

    if (error) {
      return (
        <div style={defaultContainerStyle}>
          <Alert
            message="Lỗi tải file"
            description={error}
            type="error"
            showIcon
            action={
              <Button size="small" icon={<ReloadOutlined />} onClick={loadWordDocument}>
                Thử lại
              </Button>
            }
          />
        </div>
      );
    }

    return (
      <div style={defaultContainerStyle} className="docx-viewer">
        <div
          ref={containerRef} // Container để render docx
          style={{
            padding: "20px",
            height: "100%",
            overflow: "auto",
            backgroundColor: "#fff",
          }}
        />
      </div>
    );
  };

  // Tab items (cập nhật label cho tab mammoth thành docx nếu muốn)
  const tabItems = useMemo(() => {
    const items = [];

    if (viewerMode === "office" || viewerMode === "both") {
      items.push({
        key: "office",
        label: (
          <span>
            <FileWordOutlined />
            Office Online
          </span>
        ),
        children: <OfficeViewer />,
      });
    }

    if (viewerMode === "mammoth" || viewerMode === "both") {
      // Giữ key "mammoth" để tương thích, nhưng dùng DocxViewer
      items.push({
        key: "mammoth",
        label: (
          <span>
            <FileWordOutlined />
            DOCX Preview {/* Đổi label nếu muốn */}
          </span>
        ),
        children: <DocxViewer />,
      });
    }

    return items;
  }, [viewerMode, officeViewerUrl, arrayBuffer, loading, error]); // Cập nhật deps

  // Single viewer mode (cập nhật cho mammoth thành DocxViewer)
  if (viewerMode === "office") {
    return (
      <Modal
        title={`Xem Word: ${fileName}`}
        open={open}
        onCancel={onClose}
        width={width}
        style={{top: 10}}
        styles={{
          body: {
            height: height,
            padding: 0,
          },
        }}
        footer={null}
        destroyOnClose={destroyOnClose}
        maskClosable={true}
        keyboard={true}>
        <OfficeViewer />
      </Modal>
    );
  }

  if (viewerMode === "mammoth") {
    // Giữ key nhưng dùng DocxViewer
    return (
      <Modal
        title={`Xem Word: ${fileName}`}
        open={open}
        onCancel={onClose}
        width={width}
        style={{top: 10}}
        styles={{
          body: {
            height: height,
            padding: 0,
          },
        }}
        footer={null}
        destroyOnClose={destroyOnClose}
        maskClosable={true}
        keyboard={true}>
        <DocxViewer />
      </Modal>
    );
  }

  // Both modes with tabs (giữ nguyên, nhưng DocxViewer sẽ thay thế)
  return (
    <Modal
      className="modal-word-viewer"
      title={
        <Space>
          <span>Xem Word: {fileName}</span>
          <Button type="link" size="small" icon={<DownloadOutlined />} href={fileUrl} target="_blank">
            Tải về
          </Button>
        </Space>
      }
      open={open}
      onCancel={onClose}
      width={width}
      style={{top: 10}}
      styles={{
        body: {
          height: height,
          padding: 0,
          overflow: "hidden",
        },
      }}
      footer={null}
      destroyOnClose={destroyOnClose}
      maskClosable={true}
      keyboard={true}>
      <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} style={{height: "100%"}} tabBarStyle={{margin: 0, paddingLeft: 16, paddingRight: 16}} />
    </Modal>
  );
};

WordViewerComponent.displayName = "WordViewer";
export const WordViewer = memo(WordViewerComponent, isEqual);

// Export types
export type {IWordViewerProps} from "./types";

// Export hook
export {useWordViewer} from "./useWordViewer";

// Export presets
export {WordViewerSmall, WordViewerMedium, WordViewerFullscreen, WordViewerOfficeOnly, WordViewerMammothOnly, WordViewerMobile} from "./WordViewerPresets";

export default WordViewer;
